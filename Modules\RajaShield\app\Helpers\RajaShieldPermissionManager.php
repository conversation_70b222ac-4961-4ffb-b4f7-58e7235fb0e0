<?php

namespace Modules\RajaShield\Helpers;

use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Filament\Pages\Page;
use Filament\Widgets\Widget;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use ReflectionClass;

class RajaShieldPermissionManager
{
    /**
     * Permission entity types
     */
    const ENTITY_TYPE_RESOURCE = 'resource';
    const ENTITY_TYPE_PAGE = 'page';
    const ENTITY_TYPE_WIDGET = 'widget';
    
    /**
     * Permission actions for resources
     */
    const RESOURCE_PERMISSION_ACTIONS = [
        'view',
        'view_any',
        'create',
        'update',
        'delete',
        'delete_any',
    ];
    
    /**
     * Permission actions for pages
     */
    const PAGE_PERMISSION_ACTION = 'view';
    
    /**
     * Permission actions for widgets
     */
    const WIDGET_PERMISSION_ACTION = 'view';
    
    /**
     * Get all resources from Filament
     */
    public static function getResources(): Collection
    {
        return collect(Filament::getResources())
            ->filter(function ($resource) {
                return static::isResourceEntityEnabled($resource);
            });
    }
    
    /**
     * Get all pages from Filament
     */
    public static function getPages(): Collection
    {
        return collect(Filament::getPages())
            ->filter(function ($page) {
                return static::isPageEntityEnabled($page);
            });
    }
    
    /**
     * Get all widgets from Filament
     */
    public static function getWidgets(): Collection
    {
        return collect(Filament::getWidgets())
            ->filter(function ($widget) {
                return static::isWidgetEntityEnabled($widget);
            });
    }
    
    /**
     * Check if a resource should be included in permissions
     */
    public static function isResourceEntityEnabled(string $resource): bool
    {
        // Skip RajaShield's own resources to avoid circular dependencies
        if (Str::startsWith($resource, 'Modules\\RajaShield\\Filament\\Resources\\')) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if a page should be included in permissions
     */
    public static function isPageEntityEnabled(string $page): bool
    {
        // Skip default Filament pages like Dashboard
        $defaultPages = [
            'Filament\Pages\Dashboard',
        ];
        
        return !in_array($page, $defaultPages);
    }
    
    /**
     * Check if a widget should be included in permissions
     */
    public static function isWidgetEntityEnabled(string $widget): bool
    {
        // Skip default Filament widgets
        $defaultWidgets = [
            'Filament\Widgets\AccountWidget',
            'Filament\Widgets\FilamentInfoWidget',
        ];
        
        return !in_array($widget, $defaultWidgets);
    }
    
    /**
     * Generate permission string for a resource action
     */
    public static function generateResourcePermissionName(string $resource, string $action): string
    {
        $resourceName = static::getResourceSlug($resource);
        return "{$action}_{$resourceName}";
    }
    
    /**
     * Generate permission string for a page
     */
    public static function generatePagePermissionName(string $page): string
    {
        $pageName = static::getPageSlug($page);
        return "view_page_{$pageName}";
    }
    
    /**
     * Generate permission string for a widget
     */
    public static function generateWidgetPermissionName(string $widget): string
    {
        $widgetName = static::getWidgetSlug($widget);
        return "view_widget_{$widgetName}";
    }
    
    /**
     * Get the resource slug from class name
     */
    public static function getResourceSlug(string $resource): string
    {
        return Str::of(class_basename($resource))
            ->beforeLast('Resource')
            ->kebab()
            ->plural()
            ->toString();
    }
    
    /**
     * Get the page slug from class name
     */
    public static function getPageSlug(string $page): string
    {
        return Str::of(class_basename($page))
            ->kebab()
            ->toString();
    }
    
    /**
     * Get the widget slug from class name
     */
    public static function getWidgetSlug(string $widget): string
    {
        return Str::of(class_basename($widget))
            ->beforeLast('Widget')
            ->kebab()
            ->toString();
    }
    
    /**
     * Get the entity type label
     */
    public static function getEntityTypeLabel(string $type): string
    {
        return match ($type) {
            static::ENTITY_TYPE_RESOURCE => 'Resource',
            static::ENTITY_TYPE_PAGE => 'Page',
            static::ENTITY_TYPE_WIDGET => 'Widget',
            default => 'Entity',
        };
    }
    
    /**
     * Get the entity label
     */
    public static function getEntityLabel(string $entity, string $type): string
    {
        return match ($type) {
            static::ENTITY_TYPE_RESOURCE => static::getResourceLabel($entity),
            static::ENTITY_TYPE_PAGE => static::getPageLabel($entity),
            static::ENTITY_TYPE_WIDGET => static::getWidgetLabel($entity),
            default => class_basename($entity),
        };
    }
    
    /**
     * Get the resource label
     */
    public static function getResourceLabel(string $resource): string
    {
        if (method_exists($resource, 'getModelLabel')) {
            return $resource::getModelLabel();
        }
        
        return Str::of(static::getResourceSlug($resource))
            ->headline()
            ->toString();
    }
    
    /**
     * Get the page label
     */
    public static function getPageLabel(string $page): string
    {
        if (method_exists($page, 'getNavigationLabel')) {
            return $page::getNavigationLabel();
        }
        
        return Str::of(static::getPageSlug($page))
            ->headline()
            ->toString();
    }
    
    /**
     * Get the widget label
     */
    public static function getWidgetLabel(string $widget): string
    {
        return Str::of(static::getWidgetSlug($widget))
            ->headline()
            ->toString();
    }
    
    /**
     * Generate all permissions for Filament entities
     */
    public static function generateAllPermissions(): array
    {
        $permissions = [];
        
        // Generate resource permissions
        foreach (static::getResources() as $resource) {
            foreach (static::RESOURCE_PERMISSION_ACTIONS as $action) {
                $permissions[] = [
                    'name' => static::generateResourcePermissionName($resource, $action),
                    'group' => 'resource',
                    'entity_type' => static::ENTITY_TYPE_RESOURCE,
                    'entity' => $resource,
                    'action' => $action,
                    'description' => Str::title(str_replace('_', ' ', $action)) . ' ' . static::getResourceLabel($resource),
                ];
            }
        }
        
        // Generate page permissions
        foreach (static::getPages() as $page) {
            $permissions[] = [
                'name' => static::generatePagePermissionName($page),
                'group' => 'page',
                'entity_type' => static::ENTITY_TYPE_PAGE,
                'entity' => $page,
                'action' => static::PAGE_PERMISSION_ACTION,
                'description' => 'View ' . static::getPageLabel($page) . ' Page',
            ];
        }
        
        // Generate widget permissions
        foreach (static::getWidgets() as $widget) {
            $permissions[] = [
                'name' => static::generateWidgetPermissionName($widget),
                'group' => 'widget',
                'entity_type' => static::ENTITY_TYPE_WIDGET,
                'entity' => $widget,
                'action' => static::WIDGET_PERMISSION_ACTION,
                'description' => 'View ' . static::getWidgetLabel($widget) . ' Widget',
            ];
        }
        
        return $permissions;
    }
    
    /**
     * Group permissions by entity
     */
    public static function getPermissionsGroupedByEntity(): Collection
    {
        $permissions = collect(static::generateAllPermissions());
        
        return $permissions->groupBy(function ($permission) {
            $entityType = $permission['entity_type'];
            $entity = $permission['entity'];
            
            return $entityType . '_' . $entity;
        })->sortKeys();
    }
} 