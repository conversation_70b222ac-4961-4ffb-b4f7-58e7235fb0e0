<?php

namespace Modules\RajaShield\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\RajaShield\Filament\Resources\RoleResource\Pages;
use Modules\RajaShield\Helpers\RajaShieldPermissionManager;
use Modules\RajaShield\Models\Role;
use Modules\RajaShield\Models\Permission;

class RoleResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    
    protected static ?string $navigationGroup = 'RajaShield';
    
    protected static ?int $navigationSort = 1;

    public static function getNavigationLabel(): string
    {
        return 'Peran';
    }

    public static function getModelLabel(): string
    {
        return 'Peran';
    }

    public static function getPluralModelLabel(): string
    {
        return 'Peran';
    }

    public static function form(Form $form): Form
    {
        $permissionSchema = [];
        
        // Basic role information section
        $permissionSchema[] = Forms\Components\Section::make('Informasi Peran')
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Nama')
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('description')
                    ->label('Deskripsi')
                    ->maxLength(255),
            ])
            ->columns(2);
        
        // Get permissions grouped by entity
        $permissionsByEntity = RajaShieldPermissionManager::getPermissionsGroupedByEntity();
        
        // Create tabs for Resources, Pages, and Widgets
        $resourcePermissions = [];
        $pagePermissions = [];
        $widgetPermissions = [];
        
        // Process each entity group
        foreach ($permissionsByEntity as $entityKey => $permissions) {
            // Get the first permission to determine entity type
            $firstPermission = $permissions->first();
            $entityType = $firstPermission['entity_type'];
            $entity = $firstPermission['entity'];
            $entityLabel = RajaShieldPermissionManager::getEntityLabel($entity, $entityType);
            
            // Create a section for this entity
            $entitySection = Forms\Components\Section::make($entityLabel)
                ->description($entityType)
                ->schema(function () use ($permissions) {
                    $checkboxes = [];
                    
                    // Add "Select all" button for this entity
                    $checkboxes[] = Forms\Components\Actions::make([
                        Forms\Components\Actions\Action::make('select_all_' . Str::slug($permissions->first()['entity']))
                            ->label('Pilih Semua')
                            ->color('warning')
                            ->size('sm')
                            ->action(function ($livewire) use ($permissions) {
                                $permissionIds = $permissions->pluck('name')->toArray();
                                $existingPermissions = $livewire->data['permissions'] ?? [];
                                $livewire->data['permissions'] = array_unique(array_merge($existingPermissions, $permissionIds));
                            }),
                    ]);
                    
                    // Add permission checkboxes
                    foreach ($permissions as $permission) {
                        $checkboxes[] = Forms\Components\Checkbox::make('permissions.' . $permission['name'])
                            ->label($permission['action'])
                            ->helperText($permission['description'])
                            ->inline();
                    }
                    
                    return $checkboxes;
                })->columns(3)
                ->columnSpan(1)
                ->collapsible();
            
            // Add to the appropriate tab based on entity type
            switch ($entityType) {
                case RajaShieldPermissionManager::ENTITY_TYPE_RESOURCE:
                    $resourcePermissions[] = $entitySection;
                    break;
                case RajaShieldPermissionManager::ENTITY_TYPE_PAGE:
                    $pagePermissions[] = $entitySection;
                    break;
                case RajaShieldPermissionManager::ENTITY_TYPE_WIDGET:
                    $widgetPermissions[] = $entitySection;
                    break;
            }
        }
        
        // Create tabs for Resources, Pages, and Widgets
        $permissionSchema[] = Forms\Components\Tabs::make('Izin')
            ->tabs([
                Forms\Components\Tabs\Tab::make('Resource')
                    ->icon('heroicon-o-rectangle-stack')
                    ->schema($resourcePermissions),
                Forms\Components\Tabs\Tab::make('Halaman')
                    ->icon('heroicon-o-document-text')
                    ->schema($pagePermissions),
                Forms\Components\Tabs\Tab::make('Widget')
                    ->icon('heroicon-o-squares-2x2')
                    ->schema($widgetPermissions),
            ])
            ->columnSpanFull();
        
        return $form->schema($permissionSchema)->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('permissions_count')
                    ->counts('permissions')
                    ->label('Jumlah Izin')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Diperbarui Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make()->label('Edit'),
                Tables\Actions\DeleteAction::make()->label('Hapus'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()->label('Hapus'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }
    

} 